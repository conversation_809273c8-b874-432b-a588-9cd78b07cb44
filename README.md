# ⚽ FutbolApp - Aplicación Móvil para Reservas de Canchas

Una aplicación móvil completa desarrollada con React Native y Expo para reservar canchas deportivas, crear partidos y gestionar pagos divididos.

## 🚀 Características Principales

### ✅ Funcionalidades Implementadas

- **🔐 Autenticación completa** - Registro, login y gestión de perfiles
- **🔍 Búsqueda avanzada** - Filtros, mapa interactivo y calendario de disponibilidad
- **⚽ Sistema de partidos** - Crear partidos, invitar jugadores, chat integrado
- **💳 Pagos seguros** - Integración con Stripe, pago dividido, comprobantes digitales
- **📍 Mapas y ubicación** - Canchas cercanas con detalles y reseñas
- **⭐ Sistema de reputación** - Historial de asistencia y bloqueo de usuarios
- **💰 Precios dinámicos** - Descuentos en horarios valle configurables
- **💬 Chat en tiempo real** - Comunicación entre jugadores del partido
- **🧾 Facturación electrónica** - Boletas y facturas digitales

### 🎨 Diseño

- **Colores**: Rojo (#ef4444), Verde (#22c55e) y Blanco
- **UI/UX**: Diseño moderno y intuitivo con NativeWind (Tailwind CSS)
- **Responsive**: Optimizado para diferentes tamaños de pantalla

## 🛠️ Tecnologías Utilizadas

- **Frontend**: React Native + Expo
- **Styling**: NativeWind (Tailwind CSS para React Native)
- **Base de datos**: Supabase (PostgreSQL)
- **Autenticación**: Supabase Auth
- **Pagos**: Stripe
- **Mapas**: React Native Maps
- **Navegación**: React Navigation
- **Notificaciones**: Expo Notifications

## 📋 Requisitos Previos

- Node.js (v16 o superior)
- npm o yarn
- Expo CLI
- Cuenta de Supabase
- Cuenta de Stripe (para pagos)

## 🚀 Instalación y Configuración

### 1. Clonar el repositorio
```bash
git clone <tu-repositorio>
cd futbol-app
```

### 2. Instalar dependencias
```bash
npm install
```

### 3. Configurar Supabase

1. Crea un proyecto en [Supabase](https://supabase.com)
2. Ejecuta el script SQL en `supabase/schema.sql` en el SQL Editor
3. Configura las variables en `src/config/supabase.js`:

```javascript
const supabaseUrl = 'TU_SUPABASE_URL';
const supabaseAnonKey = 'TU_SUPABASE_ANON_KEY';
```

### 4. Configurar Stripe (Opcional)

1. Crea una cuenta en [Stripe](https://stripe.com)
2. Obtén tus claves de API
3. Configura las variables de entorno para pagos

### 5. Ejecutar la aplicación

```bash
npm start
```

Escanea el código QR con Expo Go en tu dispositivo móvil.

## 📱 Pantallas Principales

### 🏠 Inicio
- Dashboard con canchas cercanas
- Partidos próximos
- Acciones rápidas

### 🔍 Búsqueda
- Filtros por deporte, superficie, precio
- Mapa interactivo con marcadores
- Calendario de disponibilidad

### ⚽ Partidos
- Lista de partidos disponibles
- Crear nuevos partidos
- Gestión de participantes
- Chat integrado

### 👤 Perfil
- Información personal
- Estadísticas y reputación
- Historial de partidos
- Configuraciones

### 💳 Pagos
- Múltiples métodos de pago
- Pago dividido entre jugadores
- Comprobantes digitales
- Facturación electrónica

## 🗄️ Base de Datos

La aplicación utiliza Supabase con las siguientes tablas principales:

- `users` - Información de usuarios
- `venues` - Canchas deportivas
- `matches` - Partidos creados
- `reservations` - Reservas de canchas
- `payments` - Transacciones de pago
- `chat_messages` - Mensajes del chat
- `notifications` - Notificaciones push

Ver el diagrama completo en el archivo `supabase/schema.sql`.

## 🔧 Configuración Adicional

### Mapas (React Native Maps)
Para usar mapas en dispositivos reales, necesitarás:
- API Key de Google Maps (Android)
- Configuración de Apple Maps (iOS)

### Notificaciones Push
Configura Expo Notifications para recibir notificaciones:
- Permisos de notificación
- Tokens de dispositivo
- Servidor de notificaciones

### Pagos con Stripe
Para pagos reales:
- Claves de API de Stripe
- Configuración de webhooks
- Cumplimiento PCI DSS

## 🧪 Testing

```bash
# Ejecutar tests (cuando estén implementados)
npm test

# Linting
npm run lint

# Formateo de código
npm run format
```

## 📦 Build y Deployment

### Build para desarrollo
```bash
expo build:android
expo build:ios
```

### Build para producción
```bash
eas build --platform android
eas build --platform ios
```

## 🤝 Contribución

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/AmazingFeature`)
3. Commit tus cambios (`git commit -m 'Add some AmazingFeature'`)
4. Push a la rama (`git push origin feature/AmazingFeature`)
5. Abre un Pull Request

## 📄 Licencia

Este proyecto está bajo la Licencia MIT. Ver `LICENSE` para más detalles.

## 📞 Soporte

Para soporte técnico o preguntas:
- Email: <EMAIL>
- Issues: [GitHub Issues](link-to-issues)

## 🔄 Próximas Funcionalidades

- [ ] Integración con redes sociales
- [ ] Sistema de torneos
- [ ] Streaming en vivo de partidos
- [ ] IA para recomendaciones de jugadores
- [ ] Integración con wearables
- [ ] Análisis de rendimiento

---

**Desarrollado con ❤️ para la comunidad futbolera**
