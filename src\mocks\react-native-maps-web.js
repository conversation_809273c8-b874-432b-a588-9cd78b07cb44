// Mock de react-native-maps para web
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

// Mock del componente MapView
const MapView = ({ children, style, ...props }) => {
  return (
    <View style={[styles.mockMapView, style]}>
      <Text style={styles.mockText}>🗺️ Mapa (Web)</Text>
      {children}
    </View>
  );
};

// Mock del componente Marker
const Marker = ({ title, description, ...props }) => {
  return (
    <View style={styles.mockMarker}>
      <Text style={styles.mockMarkerText}>📍 {title}</Text>
      {description && <Text style={styles.mockMarkerDesc}>{description}</Text>}
    </View>
  );
};

// Mock de otros componentes comunes
const Polygon = ({ ...props }) => <View />;
const Polyline = ({ ...props }) => <View />;
const Circle = ({ ...props }) => <View />;
const Callout = ({ children, ...props }) => <View>{children}</View>;

const styles = StyleSheet.create({
  mockMapView: {
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#ddd',
    minHeight: 200,
  },
  mockText: {
    fontSize: 16,
    color: '#666',
    fontWeight: 'bold',
  },
  mockMarker: {
    backgroundColor: '#fff',
    padding: 8,
    borderRadius: 4,
    margin: 4,
    borderWidth: 1,
    borderColor: '#ddd',
  },
  mockMarkerText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  mockMarkerDesc: {
    fontSize: 12,
    color: '#666',
  },
});

// Exportar como default y named exports
export default MapView;
export { Marker, Polygon, Polyline, Circle, Callout };

// También exportar todo como un objeto para compatibilidad
module.exports = {
  default: MapView,
  Marker,
  Polygon,
  Polyline,
  Circle,
  Callout,
};
