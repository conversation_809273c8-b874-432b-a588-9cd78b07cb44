export declare const FadeInData: {
    FadeIn: {
        name: string;
        style: {
            0: {
                opacity: number;
            };
            100: {
                opacity: number;
            };
        };
        duration: number;
    };
    FadeInRight: {
        name: string;
        style: {
            0: {
                opacity: number;
                transform: {
                    translateX: string;
                }[];
            };
            100: {
                opacity: number;
                transform: {
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    FadeInLeft: {
        name: string;
        style: {
            0: {
                opacity: number;
                transform: {
                    translateX: string;
                }[];
            };
            100: {
                opacity: number;
                transform: {
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    FadeInUp: {
        name: string;
        style: {
            0: {
                opacity: number;
                transform: {
                    translateY: string;
                }[];
            };
            100: {
                opacity: number;
                transform: {
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
    FadeInDown: {
        name: string;
        style: {
            0: {
                opacity: number;
                transform: {
                    translateY: string;
                }[];
            };
            100: {
                opacity: number;
                transform: {
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
};
export declare const FadeOutData: {
    FadeOut: {
        name: string;
        style: {
            0: {
                opacity: number;
            };
            100: {
                opacity: number;
            };
        };
        duration: number;
    };
    FadeOutRight: {
        name: string;
        style: {
            0: {
                opacity: number;
                transform: {
                    translateX: string;
                }[];
            };
            100: {
                opacity: number;
                transform: {
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    FadeOutLeft: {
        name: string;
        style: {
            0: {
                opacity: number;
                transform: {
                    translateX: string;
                }[];
            };
            100: {
                opacity: number;
                transform: {
                    translateX: string;
                }[];
            };
        };
        duration: number;
    };
    FadeOutUp: {
        name: string;
        style: {
            0: {
                opacity: number;
                transform: {
                    translateY: string;
                }[];
            };
            100: {
                opacity: number;
                transform: {
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
    FadeOutDown: {
        name: string;
        style: {
            0: {
                opacity: number;
                transform: {
                    translateY: string;
                }[];
            };
            100: {
                opacity: number;
                transform: {
                    translateY: string;
                }[];
            };
        };
        duration: number;
    };
};
export declare const FadeIn: {
    FadeIn: {
        style: string;
        duration: number;
    };
    FadeInRight: {
        style: string;
        duration: number;
    };
    FadeInLeft: {
        style: string;
        duration: number;
    };
    FadeInUp: {
        style: string;
        duration: number;
    };
    FadeInDown: {
        style: string;
        duration: number;
    };
};
export declare const FadeOut: {
    FadeOut: {
        style: string;
        duration: number;
    };
    FadeOutRight: {
        style: string;
        duration: number;
    };
    FadeOutLeft: {
        style: string;
        duration: number;
    };
    FadeOutUp: {
        style: string;
        duration: number;
    };
    FadeOutDown: {
        style: string;
        duration: number;
    };
};
//# sourceMappingURL=Fade.web.d.ts.map