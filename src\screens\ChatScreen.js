import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  FlatList,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../config/colors';
import { supabase } from '../config/supabase';

export default function ChatScreen({ route, navigation }) {
  const { matchId } = route.params;
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const [currentUser, setCurrentUser] = useState(null);
  const flatListRef = useRef(null);

  useEffect(() => {
    getCurrentUser();
    loadMessages();
    // Aquí se configuraría la suscripción en tiempo real con Supabase
    // const subscription = supabase
    //   .channel('chat_messages')
    //   .on('postgres_changes', { event: 'INSERT', schema: 'public', table: 'chat_messages' }, handleNewMessage)
    //   .subscribe();
    
    // return () => {
    //   subscription.unsubscribe();
    // };
  }, []);

  const getCurrentUser = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    setCurrentUser(user);
  };

  const loadMessages = () => {
    // Datos de ejemplo para demo
    const demoMessages = [
      {
        id: 1,
        user_id: 'user1',
        user_name: 'Juan Pérez',
        message: '¡Hola a todos! ¿Listos para el partido?',
        message_type: 'text',
        created_at: '2024-01-15T10:00:00Z',
        is_own: false
      },
      {
        id: 2,
        user_id: 'user2',
        user_name: 'María García',
        message: '¡Sí! Ya tengo todo listo. ¿Alguien sabe si hay estacionamiento?',
        message_type: 'text',
        created_at: '2024-01-15T10:05:00Z',
        is_own: false
      },
      {
        id: 3,
        user_id: 'current_user',
        user_name: 'Tú',
        message: 'Sí, hay estacionamiento gratuito. Nos vemos a las 6!',
        message_type: 'text',
        created_at: '2024-01-15T10:10:00Z',
        is_own: true
      },
      {
        id: 4,
        user_id: 'system',
        user_name: 'Sistema',
        message: 'Carlos López se unió al partido',
        message_type: 'system',
        created_at: '2024-01-15T10:15:00Z',
        is_own: false
      },
      {
        id: 5,
        user_id: 'user3',
        user_name: 'Carlos López',
        message: '¡Hola! Gracias por aceptarme. ¿Necesito llevar algo especial?',
        message_type: 'text',
        created_at: '2024-01-15T10:16:00Z',
        is_own: false
      },
      {
        id: 6,
        user_id: 'user1',
        user_name: 'Juan Pérez',
        message: 'Solo tus ganas de jugar 😄 Tenemos balones y conos',
        message_type: 'text',
        created_at: '2024-01-15T10:20:00Z',
        is_own: false
      },
    ];
    
    setMessages(demoMessages);
  };

  const sendMessage = async () => {
    if (!newMessage.trim()) return;

    const message = {
      id: Date.now(),
      user_id: 'current_user',
      user_name: 'Tú',
      message: newMessage.trim(),
      message_type: 'text',
      created_at: new Date().toISOString(),
      is_own: true
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');

    // Aquí iría la lógica para enviar el mensaje a Supabase
    // await supabase
    //   .from('chat_messages')
    //   .insert({
    //     room_id: roomId,
    //     user_id: currentUser.id,
    //     message: newMessage.trim(),
    //     message_type: 'text'
    //   });

    // Scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderMessage = ({ item }) => {
    if (item.message_type === 'system') {
      return (
        <View style={styles.systemMessage}>
          <Text style={styles.systemMessageText}>{item.message}</Text>
          <Text style={styles.messageTime}>{formatTime(item.created_at)}</Text>
        </View>
      );
    }

    return (
      <View style={[
        styles.messageContainer,
        item.is_own ? styles.ownMessage : styles.otherMessage
      ]}>
        {!item.is_own && (
          <Text style={styles.senderName}>{item.user_name}</Text>
        )}
        <View style={[
          styles.messageBubble,
          item.is_own ? styles.ownMessageBubble : styles.otherMessageBubble
        ]}>
          <Text style={[
            styles.messageText,
            item.is_own ? styles.ownMessageText : styles.otherMessageText
          ]}>
            {item.message}
          </Text>
        </View>
        <Text style={[
          styles.messageTime,
          item.is_own ? styles.ownMessageTime : styles.otherMessageTime
        ]}>
          {formatTime(item.created_at)}
        </Text>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Match Info Header */}
      <View style={styles.matchInfo}>
        <View style={styles.matchDetails}>
          <Text style={styles.matchTitle}>Fútbol 7 - Nivel Intermedio</Text>
          <Text style={styles.matchSubtitle}>Hoy 18:00 • Cancha Los Campeones</Text>
        </View>
        <View style={styles.participantCount}>
          <Ionicons name="people" size={16} color={colors.gray[600]} />
          <Text style={styles.participantText}>8/14</Text>
        </View>
      </View>

      {/* Messages List */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id.toString()}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        showsVerticalScrollIndicator={false}
        onContentSizeChange={() => flatListRef.current?.scrollToEnd({ animated: true })}
      />

      {/* Input Area */}
      <View style={styles.inputContainer}>
        <View style={styles.inputWrapper}>
          <TextInput
            style={styles.textInput}
            value={newMessage}
            onChangeText={setNewMessage}
            placeholder="Escribe un mensaje..."
            multiline
            maxLength={500}
          />
          <TouchableOpacity style={styles.attachButton}>
            <Ionicons name="attach" size={20} color={colors.gray[500]} />
          </TouchableOpacity>
        </View>
        <TouchableOpacity 
          style={[
            styles.sendButton,
            newMessage.trim() ? styles.sendButtonActive : styles.sendButtonInactive
          ]}
          onPress={sendMessage}
          disabled={!newMessage.trim()}
        >
          <Ionicons 
            name="send" 
            size={20} 
            color={newMessage.trim() ? colors.white : colors.gray[400]} 
          />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  matchInfo: {
    backgroundColor: colors.white,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  matchDetails: {
    flex: 1,
  },
  matchTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 2,
  },
  matchSubtitle: {
    fontSize: 14,
    color: colors.gray[600],
  },
  participantCount: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: colors.gray[100],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  participantText: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.gray[700],
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
    paddingBottom: 8,
  },
  messageContainer: {
    marginBottom: 16,
    maxWidth: '80%',
  },
  ownMessage: {
    alignSelf: 'flex-end',
    alignItems: 'flex-end',
  },
  otherMessage: {
    alignSelf: 'flex-start',
    alignItems: 'flex-start',
  },
  senderName: {
    fontSize: 12,
    fontWeight: '600',
    color: colors.gray[600],
    marginBottom: 4,
    marginLeft: 12,
  },
  messageBubble: {
    borderRadius: 18,
    paddingHorizontal: 16,
    paddingVertical: 10,
    maxWidth: '100%',
  },
  ownMessageBubble: {
    backgroundColor: colors.primary[500],
    borderBottomRightRadius: 4,
  },
  otherMessageBubble: {
    backgroundColor: colors.white,
    borderBottomLeftRadius: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 20,
  },
  ownMessageText: {
    color: colors.white,
  },
  otherMessageText: {
    color: colors.gray[900],
  },
  messageTime: {
    fontSize: 11,
    marginTop: 4,
  },
  ownMessageTime: {
    color: colors.gray[400],
    textAlign: 'right',
  },
  otherMessageTime: {
    color: colors.gray[500],
    marginLeft: 12,
  },
  systemMessage: {
    alignSelf: 'center',
    alignItems: 'center',
    marginBottom: 16,
    backgroundColor: colors.gray[100],
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
  },
  systemMessageText: {
    fontSize: 14,
    color: colors.gray[600],
    fontStyle: 'italic',
    textAlign: 'center',
  },
  inputContainer: {
    backgroundColor: colors.white,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 8,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: colors.gray[100],
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    maxHeight: 100,
  },
  textInput: {
    flex: 1,
    fontSize: 16,
    color: colors.gray[900],
    maxHeight: 80,
    paddingVertical: 4,
  },
  attachButton: {
    padding: 4,
    marginLeft: 8,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonActive: {
    backgroundColor: colors.primary[500],
  },
  sendButtonInactive: {
    backgroundColor: colors.gray[200],
  },
});
