{"name": "futbol-app", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:all": "eas build --platform all", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "update": "eas update", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "format": "prettier --write .", "test": "jest", "test:watch": "jest --watch", "clean": "expo r -c", "reset": "rm -rf node_modules && npm install"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.7", "@react-navigation/native": "^7.1.17", "@react-navigation/stack": "^7.4.8", "@stripe/stripe-react-native": "^0.52.0", "@supabase/supabase-js": "^2.57.4", "bcryptjs": "^3.0.2", "expo": "~54.0.7", "expo-image-picker": "^17.0.8", "expo-location": "^19.0.7", "expo-notifications": "^0.32.11", "expo-status-bar": "~3.0.8", "mysql2": "^3.14.5", "nativewind": "^4.2.1", "react": "19.1.0", "react-native": "0.81.4", "react-native-calendars": "^1.1313.0", "react-native-gesture-handler": "^2.28.0", "react-native-keychain": "^10.0.0", "react-native-maps": "^1.26.6", "react-native-reanimated": "^4.1.0", "react-native-safe-area-context": "^5.6.1", "react-native-screens": "^4.16.0", "react-native-svg": "^15.13.0", "react-native-vector-icons": "^10.3.0"}, "private": true, "devDependencies": {"tailwindcss": "^3.4.17"}}