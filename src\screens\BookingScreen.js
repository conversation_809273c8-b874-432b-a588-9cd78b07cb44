import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Switch,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../config/colors';

export default function BookingScreen({ route, navigation }) {
  const { venue, selectedTime, price, date } = route.params;
  const [splitPayment, setSplitPayment] = useState(false);
  const [maxPlayers, setMaxPlayers] = useState(14);
  const [duration, setDuration] = useState(90);

  const calculateTotal = () => {
    const hours = duration / 60;
    return (price * hours).toFixed(2);
  };

  const calculatePerPlayer = () => {
    if (!splitPayment) return calculateTotal();
    return (calculateTotal() / maxPlayers).toFixed(2);
  };

  const handleBooking = () => {
    const bookingData = {
      venue,
      date,
      time: selectedTime,
      duration,
      totalAmount: calculateTotal(),
      splitPayment,
      maxPlayers: splitPayment ? maxPlayers : 1,
      amountPerPlayer: calculatePerPlayer(),
    };

    navigation.navigate('Payment', { booking: bookingData });
  };

  const durationOptions = [
    { value: 60, label: '1 hora' },
    { value: 90, label: '1.5 horas' },
    { value: 120, label: '2 horas' },
    { value: 180, label: '3 horas' },
  ];

  const playerOptions = [8, 10, 12, 14, 16, 18, 20, 22];

  return (
    <ScrollView style={styles.container}>
      {/* Booking Summary */}
      <View style={styles.summaryCard}>
        <Text style={styles.sectionTitle}>Resumen de Reserva</Text>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Cancha:</Text>
          <Text style={styles.summaryValue}>{venue.name}</Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Fecha:</Text>
          <Text style={styles.summaryValue}>{date}</Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Hora:</Text>
          <Text style={styles.summaryValue}>{selectedTime}</Text>
        </View>
        
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>Superficie:</Text>
          <Text style={styles.summaryValue}>{venue.surface}</Text>
        </View>
      </View>

      {/* Duration Selection */}
      <View style={styles.optionCard}>
        <Text style={styles.sectionTitle}>Duración</Text>
        <View style={styles.optionsGrid}>
          {durationOptions.map((option) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.optionButton,
                duration === option.value && styles.optionButtonActive
              ]}
              onPress={() => setDuration(option.value)}
            >
              <Text style={[
                styles.optionText,
                duration === option.value && styles.optionTextActive
              ]}>
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Split Payment Option */}
      <View style={styles.optionCard}>
        <View style={styles.splitPaymentHeader}>
          <View style={styles.splitPaymentInfo}>
            <Text style={styles.sectionTitle}>Pago Dividido</Text>
            <Text style={styles.splitPaymentDescription}>
              Permite que otros jugadores paguen su parte
            </Text>
          </View>
          <Switch
            value={splitPayment}
            onValueChange={setSplitPayment}
            trackColor={{ false: colors.gray[300], true: colors.primary[200] }}
            thumbColor={splitPayment ? colors.primary[500] : colors.gray[500]}
          />
        </View>

        {splitPayment && (
          <View style={styles.playersSelection}>
            <Text style={styles.playersLabel}>Número máximo de jugadores:</Text>
            <View style={styles.playersGrid}>
              {playerOptions.map((players) => (
                <TouchableOpacity
                  key={players}
                  style={[
                    styles.playerButton,
                    maxPlayers === players && styles.playerButtonActive
                  ]}
                  onPress={() => setMaxPlayers(players)}
                >
                  <Text style={[
                    styles.playerButtonText,
                    maxPlayers === players && styles.playerButtonTextActive
                  ]}>
                    {players}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}
      </View>

      {/* Pricing Breakdown */}
      <View style={styles.pricingCard}>
        <Text style={styles.sectionTitle}>Desglose de Precios</Text>
        
        <View style={styles.pricingRow}>
          <Text style={styles.pricingLabel}>Precio por hora:</Text>
          <Text style={styles.pricingValue}>${price}</Text>
        </View>
        
        <View style={styles.pricingRow}>
          <Text style={styles.pricingLabel}>Duración:</Text>
          <Text style={styles.pricingValue}>{duration / 60} hora(s)</Text>
        </View>
        
        <View style={styles.pricingDivider} />
        
        <View style={styles.pricingRow}>
          <Text style={styles.totalLabel}>Total:</Text>
          <Text style={styles.totalValue}>${calculateTotal()}</Text>
        </View>
        
        {splitPayment && (
          <View style={styles.pricingRow}>
            <Text style={styles.pricingLabel}>Por jugador ({maxPlayers} jugadores):</Text>
            <Text style={styles.perPlayerValue}>${calculatePerPlayer()}</Text>
          </View>
        )}
      </View>

      {/* Important Notes */}
      <View style={styles.notesCard}>
        <Text style={styles.sectionTitle}>Información Importante</Text>
        
        <View style={styles.noteItem}>
          <Ionicons name="time" size={16} color={colors.primary[500]} />
          <Text style={styles.noteText}>
            Llega 10 minutos antes de tu hora reservada
          </Text>
        </View>
        
        <View style={styles.noteItem}>
          <Ionicons name="card" size={16} color={colors.primary[500]} />
          <Text style={styles.noteText}>
            {splitPayment 
              ? 'Cada jugador debe confirmar su pago en 24 horas'
              : 'El pago se procesa inmediatamente'
            }
          </Text>
        </View>
        
        <View style={styles.noteItem}>
          <Ionicons name="refresh" size={16} color={colors.primary[500]} />
          <Text style={styles.noteText}>
            Cancelación gratuita hasta 2 horas antes
          </Text>
        </View>
        
        {splitPayment && (
          <View style={styles.noteItem}>
            <Ionicons name="people" size={16} color={colors.warning} />
            <Text style={styles.noteText}>
              Si no se completa el pago dividido, la reserva será cancelada
            </Text>
          </View>
        )}
      </View>

      {/* Book Button */}
      <TouchableOpacity style={styles.bookButton} onPress={handleBooking}>
        <Text style={styles.bookButtonText}>
          {splitPayment ? 'Crear Reserva Compartida' : 'Proceder al Pago'}
        </Text>
        <Text style={styles.bookButtonSubtext}>
          ${splitPayment ? calculatePerPlayer() : calculateTotal()}
          {splitPayment ? ' por jugador' : ' total'}
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  summaryCard: {
    backgroundColor: colors.white,
    margin: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  summaryLabel: {
    fontSize: 16,
    color: colors.gray[600],
  },
  summaryValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
  },
  optionCard: {
    backgroundColor: colors.white,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  optionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  optionButton: {
    flex: 1,
    minWidth: '45%',
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  optionButtonActive: {
    backgroundColor: colors.primary[500],
    borderColor: colors.primary[500],
  },
  optionText: {
    fontSize: 14,
    fontWeight: '500',
    color: colors.gray[700],
  },
  optionTextActive: {
    color: colors.white,
  },
  splitPaymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  splitPaymentInfo: {
    flex: 1,
  },
  splitPaymentDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginTop: 4,
  },
  playersSelection: {
    marginTop: 16,
  },
  playersLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 12,
  },
  playersGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  playerButton: {
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    minWidth: 50,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  playerButtonActive: {
    backgroundColor: colors.secondary[500],
    borderColor: colors.secondary[500],
  },
  playerButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
  },
  playerButtonTextActive: {
    color: colors.white,
  },
  pricingCard: {
    backgroundColor: colors.white,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  pricingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  pricingLabel: {
    fontSize: 16,
    color: colors.gray[600],
  },
  pricingValue: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[900],
  },
  pricingDivider: {
    height: 1,
    backgroundColor: colors.gray[200],
    marginVertical: 12,
  },
  totalLabel: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[900],
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary[500],
  },
  perPlayerValue: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.secondary[500],
  },
  notesCard: {
    backgroundColor: colors.white,
    marginHorizontal: 16,
    marginBottom: 100,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  noteItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
    gap: 8,
  },
  noteText: {
    flex: 1,
    fontSize: 14,
    color: colors.gray[700],
    lineHeight: 20,
  },
  bookButton: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.primary[500],
    margin: 16,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  bookButtonText: {
    color: colors.white,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  bookButtonSubtext: {
    color: colors.white,
    fontSize: 14,
    opacity: 0.9,
  },
});
