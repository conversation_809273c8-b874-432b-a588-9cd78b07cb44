import type { MakeShareableClone, WorkletRuntime as WorkletRuntimeFromWorklets } from 'react-native-worklets';
import { createWorkletRuntime as createWorkletRuntimeFromWorklets, executeOnUIRuntimeSync as executeOnUIRuntimeSyncFromWorklets, isWorkletFunction as isWorkletFunctionFromWorklets, runOnJS as runOnJSFromWorklets, runOnRuntime as runOnRuntimeFromWorklets, runOn<PERSON> as runOnUIFromWorklets } from 'react-native-worklets';
/**
 * @deprecated Please import `makeShareableCloneRecursive` directly from
 *   `react-native-worklets` instead of `react-native-reanimated`.
 */
export declare const makeShareableCloneRecursive: MakeShareableClone;
/**
 * @deprecated Please import `createWorkletRuntime` directly from
 *   `react-native-worklets` instead of `react-native-reanimated`.
 */
export declare const createWorkletRuntime: typeof createWorkletRuntimeFromWorklets;
/**
 * @deprecated Please import `executeOnUIRuntimeSync` directly from
 *   `react-native-worklets` instead of `react-native-reanimated`.
 */
export declare const executeOnUIRuntimeSync: typeof executeOnUIRuntimeSyncFromWorklets;
/**
 * @deprecated Please import `runOnJS` directly from `react-native-worklets`
 *   instead of `react-native-reanimated`.
 */
export declare const runOnJS: typeof runOnJSFromWorklets;
/**
 * @deprecated Please import `runOnUI` directly from `react-native-worklets`
 *   instead of `react-native-reanimated`.
 */
export declare const runOnUI: typeof runOnUIFromWorklets;
/**
 * @deprecated Please import `runOnRuntime` directly from
 *   `react-native-worklets` instead of `react-native-reanimated`.
 */
export declare const runOnRuntime: typeof runOnRuntimeFromWorklets;
/**
 * @deprecated Please import `WorkletRuntime` directly from
 *   `react-native-worklets` instead of `react-native-reanimated`.
 */
export type WorkletRuntime = WorkletRuntimeFromWorklets;
/**
 * @deprecated Please import `isWorkletFunction` directly from
 *   `react-native-worklets` instead of `react-native-reanimated`.
 */
export declare const isWorkletFunction: typeof isWorkletFunctionFromWorklets;
//# sourceMappingURL=workletFunctions.d.ts.map