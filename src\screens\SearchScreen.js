import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Modal,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import MapView, { Marker } from 'react-native-maps';
import { Calendar } from 'react-native-calendars';
import { colors } from '../config/colors';

export default function SearchScreen({ navigation }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [showMap, setShowMap] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [selectedDate, setSelectedDate] = useState('');
  const [filters, setFilters] = useState({
    sport: 'futbol',
    surface: 'all',
    priceRange: [0, 100],
    amenities: [],
  });
  const [venues, setVenues] = useState([]);

  useEffect(() => {
    loadVenues();
  }, []);

  const loadVenues = () => {
    // Datos de ejemplo para demo
    setVenues([
      {
        id: 1,
        name: 'Cancha Los Campeones',
        address: 'Av. Principal 123',
        distance: '0.5 km',
        rating: 4.8,
        price: 25,
        surface: 'Césped sintético',
        amenities: ['Estacionamiento', 'Vestuarios', 'Iluminación'],
        coordinates: { latitude: -12.0464, longitude: -77.0428 },
        availability: {
          '2024-01-15': ['09:00', '10:00', '11:00', '16:00', '17:00', '18:00'],
          '2024-01-16': ['08:00', '09:00', '15:00', '16:00', '19:00', '20:00'],
        }
      },
      {
        id: 2,
        name: 'Complejo Deportivo Central',
        address: 'Jr. Deportes 456',
        distance: '1.2 km',
        rating: 4.6,
        price: 30,
        surface: 'Césped natural',
        amenities: ['Estacionamiento', 'Vestuarios', 'Cafetería', 'Iluminación'],
        coordinates: { latitude: -12.0564, longitude: -77.0528 },
        availability: {
          '2024-01-15': ['10:00', '11:00', '14:00', '15:00', '18:00', '19:00'],
          '2024-01-16': ['09:00', '10:00', '16:00', '17:00', '20:00', '21:00'],
        }
      },
      {
        id: 3,
        name: 'Arena Deportiva Norte',
        address: 'Av. Norte 789',
        distance: '2.1 km',
        rating: 4.4,
        price: 35,
        surface: 'Césped sintético',
        amenities: ['Estacionamiento', 'Vestuarios', 'Iluminación', 'Seguridad'],
        coordinates: { latitude: -12.0364, longitude: -77.0328 },
        availability: {
          '2024-01-15': ['08:00', '09:00', '17:00', '18:00', '19:00', '20:00'],
          '2024-01-16': ['10:00', '11:00', '15:00', '16:00', '18:00', '19:00'],
        }
      },
    ]);
  };

  const filteredVenues = venues.filter(venue => 
    venue.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    venue.address.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const renderVenueCard = ({ item }) => (
    <TouchableOpacity
      style={styles.venueCard}
      onPress={() => navigation.navigate('VenueDetail', { venue: item })}
    >
      <View style={styles.venueHeader}>
        <View style={styles.venueInfo}>
          <Text style={styles.venueName}>{item.name}</Text>
          <Text style={styles.venueAddress}>{item.address}</Text>
          <Text style={styles.venueSurface}>{item.surface}</Text>
        </View>
        <View style={styles.venueRating}>
          <Ionicons name="star" size={16} color={colors.warning} />
          <Text style={styles.rating}>{item.rating}</Text>
        </View>
      </View>
      
      <View style={styles.venueFooter}>
        <Text style={styles.distance}>{item.distance}</Text>
        <Text style={styles.price}>${item.price}/hora</Text>
      </View>
      
      <View style={styles.amenities}>
        {item.amenities.slice(0, 3).map((amenity, index) => (
          <View key={index} style={styles.amenityTag}>
            <Text style={styles.amenityText}>{amenity}</Text>
          </View>
        ))}
      </View>
    </TouchableOpacity>
  );

  const FilterModal = () => (
    <Modal visible={showFilters} animationType="slide" transparent>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Filtros</Text>
            <TouchableOpacity onPress={() => setShowFilters(false)}>
              <Ionicons name="close" size={24} color={colors.gray[600]} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.filterContent}>
            <Text style={styles.filterLabel}>Deporte</Text>
            <View style={styles.filterOptions}>
              {['futbol', 'futsal', 'basquet'].map(sport => (
                <TouchableOpacity
                  key={sport}
                  style={[
                    styles.filterOption,
                    filters.sport === sport && styles.filterOptionActive
                  ]}
                  onPress={() => setFilters(prev => ({ ...prev, sport }))}
                >
                  <Text style={[
                    styles.filterOptionText,
                    filters.sport === sport && styles.filterOptionTextActive
                  ]}>
                    {sport.charAt(0).toUpperCase() + sport.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.filterLabel}>Superficie</Text>
            <View style={styles.filterOptions}>
              {[
                { key: 'all', label: 'Todas' },
                { key: 'natural', label: 'Césped natural' },
                { key: 'synthetic', label: 'Césped sintético' },
                { key: 'concrete', label: 'Concreto' }
              ].map(surface => (
                <TouchableOpacity
                  key={surface.key}
                  style={[
                    styles.filterOption,
                    filters.surface === surface.key && styles.filterOptionActive
                  ]}
                  onPress={() => setFilters(prev => ({ ...prev, surface: surface.key }))}
                >
                  <Text style={[
                    styles.filterOptionText,
                    filters.surface === surface.key && styles.filterOptionTextActive
                  ]}>
                    {surface.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </ScrollView>
          
          <TouchableOpacity
            style={styles.applyButton}
            onPress={() => setShowFilters(false)}
          >
            <Text style={styles.applyButtonText}>Aplicar Filtros</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  const CalendarModal = () => (
    <Modal visible={showCalendar} animationType="slide" transparent>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Seleccionar Fecha</Text>
            <TouchableOpacity onPress={() => setShowCalendar(false)}>
              <Ionicons name="close" size={24} color={colors.gray[600]} />
            </TouchableOpacity>
          </View>
          
          <Calendar
            onDayPress={(day) => {
              setSelectedDate(day.dateString);
              setShowCalendar(false);
            }}
            markedDates={{
              [selectedDate]: { selected: true, selectedColor: colors.primary[500] }
            }}
            theme={{
              selectedDayBackgroundColor: colors.primary[500],
              todayTextColor: colors.primary[500],
              arrowColor: colors.primary[500],
            }}
          />
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* Search Header */}
      <View style={styles.searchHeader}>
        <View style={styles.searchContainer}>
          <Ionicons name="search" size={20} color={colors.gray[500]} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar canchas..."
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>
        
        <View style={styles.headerButtons}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowFilters(true)}
          >
            <Ionicons name="options" size={20} color={colors.gray[700]} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={styles.headerButton}
            onPress={() => setShowCalendar(true)}
          >
            <Ionicons name="calendar" size={20} color={colors.gray[700]} />
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.headerButton, showMap && styles.headerButtonActive]}
            onPress={() => setShowMap(!showMap)}
          >
            <Ionicons name="map" size={20} color={showMap ? colors.white : colors.gray[700]} />
          </TouchableOpacity>
        </View>
      </View>

      {selectedDate && (
        <View style={styles.selectedDateContainer}>
          <Text style={styles.selectedDateText}>
            Fecha seleccionada: {selectedDate}
          </Text>
          <TouchableOpacity onPress={() => setSelectedDate('')}>
            <Ionicons name="close" size={16} color={colors.gray[600]} />
          </TouchableOpacity>
        </View>
      )}

      {/* Content */}
      {showMap ? (
        <MapView
          style={styles.map}
          initialRegion={{
            latitude: -12.0464,
            longitude: -77.0428,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          }}
        >
          {filteredVenues.map(venue => (
            <Marker
              key={venue.id}
              coordinate={venue.coordinates}
              title={venue.name}
              description={`$${venue.price}/hora`}
              onCalloutPress={() => navigation.navigate('VenueDetail', { venue })}
            />
          ))}
        </MapView>
      ) : (
        <FlatList
          data={filteredVenues}
          renderItem={renderVenueCard}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.venuesList}
          showsVerticalScrollIndicator={false}
        />
      )}

      <FilterModal />
      <CalendarModal />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  searchHeader: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: colors.white,
    alignItems: 'center',
    gap: 12,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
    color: colors.gray[900],
  },
  headerButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: colors.gray[100],
  },
  headerButtonActive: {
    backgroundColor: colors.primary[500],
  },
  selectedDateContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: colors.primary[50],
    padding: 12,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
  },
  selectedDateText: {
    color: colors.primary[700],
    fontWeight: '500',
  },
  map: {
    flex: 1,
  },
  venuesList: {
    padding: 16,
  },
  venueCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  venueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  venueInfo: {
    flex: 1,
  },
  venueName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 4,
  },
  venueAddress: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 2,
  },
  venueSurface: {
    fontSize: 14,
    color: colors.gray[500],
  },
  venueRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
  },
  venueFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  distance: {
    fontSize: 14,
    color: colors.gray[600],
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary[500],
  },
  amenities: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  amenityTag: {
    backgroundColor: colors.gray[100],
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  amenityText: {
    fontSize: 12,
    color: colors.gray[700],
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.gray[900],
  },
  filterContent: {
    padding: 20,
  },
  filterLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 12,
    marginTop: 16,
  },
  filterOptions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  filterOption: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  filterOptionActive: {
    backgroundColor: colors.primary[500],
    borderColor: colors.primary[500],
  },
  filterOptionText: {
    color: colors.gray[700],
    fontWeight: '500',
  },
  filterOptionTextActive: {
    color: colors.white,
  },
  applyButton: {
    backgroundColor: colors.primary[500],
    margin: 20,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  applyButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
