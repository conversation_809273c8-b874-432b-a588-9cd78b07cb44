import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Image,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../config/colors';
import { supabase } from '../config/supabase';

export default function ProfileScreen({ navigation }) {
  const [user, setUser] = useState(null);
  const [stats, setStats] = useState({
    totalMatches: 0,
    attendedMatches: 0,
    cancelledMatches: 0,
    reputationScore: 5.0,
  });

  useEffect(() => {
    getCurrentUser();
    loadUserStats();
  }, []);

  const getCurrentUser = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    setUser(user);
  };

  const loadUserStats = () => {
    // Datos de ejemplo para demo
    setStats({
      totalMatches: 25,
      attendedMatches: 22,
      cancelledMatches: 3,
      reputationScore: 4.7,
    });
  };

  const handleLogout = async () => {
    Alert.alert(
      'Cerrar Sesión',
      '¿Estás seguro que quieres cerrar sesión?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Cerrar Sesión',
          style: 'destructive',
          onPress: async () => {
            await supabase.auth.signOut();
            navigation.replace('Login');
          }
        }
      ]
    );
  };

  const getReputationColor = (score) => {
    if (score >= 4.5) return colors.secondary[500];
    if (score >= 3.5) return colors.warning;
    return colors.primary[500];
  };

  const getAttendanceRate = () => {
    if (stats.totalMatches === 0) return 0;
    return ((stats.attendedMatches / stats.totalMatches) * 100).toFixed(1);
  };

  const menuItems = [
    {
      icon: 'person-outline',
      title: 'Editar Perfil',
      subtitle: 'Actualiza tu información personal',
      onPress: () => Alert.alert('Próximamente', 'Esta función estará disponible pronto')
    },
    {
      icon: 'time-outline',
      title: 'Historial de Partidos',
      subtitle: 'Ve todos tus partidos anteriores',
      onPress: () => Alert.alert('Próximamente', 'Esta función estará disponible pronto')
    },
    {
      icon: 'card-outline',
      title: 'Métodos de Pago',
      subtitle: 'Gestiona tus tarjetas y pagos',
      onPress: () => Alert.alert('Próximamente', 'Esta función estará disponible pronto')
    },
    {
      icon: 'receipt-outline',
      title: 'Facturas y Comprobantes',
      subtitle: 'Descarga tus comprobantes de pago',
      onPress: () => Alert.alert('Próximamente', 'Esta función estará disponible pronto')
    },
    {
      icon: 'notifications-outline',
      title: 'Notificaciones',
      subtitle: 'Configura tus preferencias',
      onPress: () => Alert.alert('Próximamente', 'Esta función estará disponible pronto')
    },
    {
      icon: 'shield-outline',
      title: 'Privacidad y Seguridad',
      subtitle: 'Gestiona tu privacidad',
      onPress: () => Alert.alert('Próximamente', 'Esta función estará disponible pronto')
    },
    {
      icon: 'help-circle-outline',
      title: 'Ayuda y Soporte',
      subtitle: 'Obtén ayuda o contacta soporte',
      onPress: () => Alert.alert('Próximamente', 'Esta función estará disponible pronto')
    },
  ];

  return (
    <ScrollView style={styles.container}>
      {/* Profile Header */}
      <View style={styles.profileHeader}>
        <View style={styles.avatarContainer}>
          <Image
            source={{
              uri: user?.user_metadata?.avatar_url || 'https://via.placeholder.com/100x100'
            }}
            style={styles.avatar}
          />
          <TouchableOpacity style={styles.editAvatarButton}>
            <Ionicons name="camera" size={16} color={colors.white} />
          </TouchableOpacity>
        </View>
        
        <Text style={styles.userName}>
          {user?.user_metadata?.full_name || 'Usuario'}
        </Text>
        <Text style={styles.userEmail}>{user?.email}</Text>
        
        <View style={styles.reputationContainer}>
          <Ionicons 
            name="star" 
            size={20} 
            color={getReputationColor(stats.reputationScore)} 
          />
          <Text style={[styles.reputationScore, { color: getReputationColor(stats.reputationScore) }]}>
            {stats.reputationScore.toFixed(1)}
          </Text>
          <Text style={styles.reputationLabel}>Reputación</Text>
        </View>
      </View>

      {/* Stats Cards */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.totalMatches}</Text>
          <Text style={styles.statLabel}>Partidos Totales</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{getAttendanceRate()}%</Text>
          <Text style={styles.statLabel}>Asistencia</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statNumber}>{stats.attendedMatches}</Text>
          <Text style={styles.statLabel}>Completados</Text>
        </View>
      </View>

      {/* Achievement Badge */}
      <View style={styles.achievementContainer}>
        <View style={styles.achievementBadge}>
          <Ionicons name="trophy" size={24} color={colors.warning} />
          <View style={styles.achievementText}>
            <Text style={styles.achievementTitle}>Jugador Confiable</Text>
            <Text style={styles.achievementSubtitle}>
              Excelente historial de asistencia
            </Text>
          </View>
        </View>
      </View>

      {/* Menu Items */}
      <View style={styles.menuContainer}>
        {menuItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.menuItem}
            onPress={item.onPress}
          >
            <View style={styles.menuItemLeft}>
              <View style={styles.menuIconContainer}>
                <Ionicons name={item.icon} size={24} color={colors.gray[600]} />
              </View>
              <View style={styles.menuTextContainer}>
                <Text style={styles.menuTitle}>{item.title}</Text>
                <Text style={styles.menuSubtitle}>{item.subtitle}</Text>
              </View>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.gray[400]} />
          </TouchableOpacity>
        ))}
      </View>

      {/* Logout Button */}
      <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
        <Ionicons name="log-out-outline" size={20} color={colors.primary[500]} />
        <Text style={styles.logoutText}>Cerrar Sesión</Text>
      </TouchableOpacity>

      {/* App Version */}
      <Text style={styles.versionText}>Versión 1.0.0</Text>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  profileHeader: {
    backgroundColor: colors.white,
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 20,
  },
  avatarContainer: {
    position: 'relative',
    marginBottom: 16,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: colors.gray[200],
  },
  editAvatarButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: colors.primary[500],
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: colors.white,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 4,
  },
  userEmail: {
    fontSize: 16,
    color: colors.gray[600],
    marginBottom: 16,
  },
  reputationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  reputationScore: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  reputationLabel: {
    fontSize: 14,
    color: colors.gray[600],
    marginLeft: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.primary[500],
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: colors.gray[600],
    textAlign: 'center',
  },
  achievementContainer: {
    paddingHorizontal: 20,
    marginBottom: 20,
  },
  achievementBadge: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  achievementText: {
    flex: 1,
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 2,
  },
  achievementSubtitle: {
    fontSize: 14,
    color: colors.gray[600],
  },
  menuContainer: {
    backgroundColor: colors.white,
    marginHorizontal: 20,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  menuTextContainer: {
    flex: 1,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 2,
  },
  menuSubtitle: {
    fontSize: 14,
    color: colors.gray[600],
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
    marginHorizontal: 20,
    marginTop: 20,
    padding: 16,
    borderRadius: 12,
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.primary[500],
  },
  versionText: {
    textAlign: 'center',
    fontSize: 12,
    color: colors.gray[500],
    marginTop: 20,
    marginBottom: 40,
  },
});
