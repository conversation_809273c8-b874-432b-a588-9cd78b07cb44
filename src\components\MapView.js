import React from 'react';
import { Platform, View, Text, StyleSheet } from 'react-native';
import { colors } from '../config/colors';
import MapView, { Marker } from 'react-native-maps';

// Componente de mapa que funciona en web y móvil
export default function CustomMapView({
  style,
  initialRegion,
  children,
  markers = [],
  ...props
}) {
  // Ahora funciona tanto en web como en móvil gracias al mock
  return (
    <MapView
      style={style}
      initialRegion={initialRegion}
      {...props}
    >
      {children}
    </MapView>
  );
}

// Componente Marker que funciona en web y móvil
export function CustomMarker({ coordinate, title, description, onCalloutPress, ...props }) {
  return (
    <Marker
      coordinate={coordinate}
      title={title}
      description={description}
      onCalloutPress={onCalloutPress}
      {...props}
    />
  );
}

// Estilos simplificados
const styles = StyleSheet.create({
  // Estilos básicos si los necesitas
});
