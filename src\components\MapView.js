import React from 'react';
import { Platform, View, Text, StyleSheet } from 'react-native';
import { colors } from '../config/colors';

// Importación condicional de react-native-maps
let MapView, Marker;

if (Platform.OS !== 'web') {
  // Solo importar en móvil
  const Maps = require('react-native-maps');
  MapView = Maps.default;
  Marker = Maps.Marker;
}

// Componente de mapa que funciona en web y móvil
export default function CustomMapView({ 
  style, 
  initialRegion, 
  children, 
  onCalloutPress,
  markers = [],
  ...props 
}) {
  
  // En web, mostrar un placeholder del mapa
  if (Platform.OS === 'web') {
    return (
      <View style={[styles.webMapContainer, style]}>
        <View style={styles.webMapHeader}>
          <Text style={styles.webMapTitle}>🗺️ Vista de Mapa</Text>
          <Text style={styles.webMapSubtitle}>
            {markers.length} ubicaciones encontradas
          </Text>
        </View>
        
        <View style={styles.webMapContent}>
          {markers.map((marker, index) => (
            <View key={index} style={styles.webMarkerCard}>
              <View style={styles.webMarkerIcon}>
                <Text style={styles.webMarkerEmoji}>📍</Text>
              </View>
              <View style={styles.webMarkerInfo}>
                <Text style={styles.webMarkerTitle}>{marker.title}</Text>
                <Text style={styles.webMarkerDescription}>
                  {marker.description}
                </Text>
                {marker.onCalloutPress && (
                  <Text 
                    style={styles.webMarkerLink}
                    onPress={marker.onCalloutPress}
                  >
                    Ver detalles →
                  </Text>
                )}
              </View>
            </View>
          ))}
          
          {markers.length === 0 && (
            <View style={styles.webMapEmpty}>
              <Text style={styles.webMapEmptyText}>
                No hay ubicaciones para mostrar
              </Text>
            </View>
          )}
        </View>
        
        <View style={styles.webMapFooter}>
          <Text style={styles.webMapNote}>
            💡 En dispositivos móviles verás el mapa interactivo
          </Text>
        </View>
      </View>
    );
  }

  // En móvil, usar react-native-maps
  return (
    <MapView
      style={style}
      initialRegion={initialRegion}
      {...props}
    >
      {children}
    </MapView>
  );
}

// Componente Marker que funciona en web y móvil
export function CustomMarker({ coordinate, title, description, onCalloutPress, ...props }) {
  if (Platform.OS === 'web') {
    // En web, el marker se renderiza en el CustomMapView
    return null;
  }

  return (
    <Marker
      coordinate={coordinate}
      title={title}
      description={description}
      onCalloutPress={onCalloutPress}
      {...props}
    />
  );
}

const styles = StyleSheet.create({
  webMapContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  webMapHeader: {
    backgroundColor: colors.primary[500],
    padding: 16,
    alignItems: 'center',
  },
  webMapTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.white,
    marginBottom: 4,
  },
  webMapSubtitle: {
    fontSize: 14,
    color: colors.white,
    opacity: 0.9,
  },
  webMapContent: {
    padding: 16,
    maxHeight: 300,
    overflow: 'scroll',
  },
  webMarkerCard: {
    flexDirection: 'row',
    backgroundColor: colors.gray[50],
    borderRadius: 8,
    padding: 12,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  webMarkerIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  webMarkerEmoji: {
    fontSize: 20,
  },
  webMarkerInfo: {
    flex: 1,
  },
  webMarkerTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 4,
  },
  webMarkerDescription: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
  },
  webMarkerLink: {
    fontSize: 14,
    color: colors.primary[500],
    fontWeight: '600',
    cursor: 'pointer',
  },
  webMapEmpty: {
    alignItems: 'center',
    padding: 32,
  },
  webMapEmptyText: {
    fontSize: 16,
    color: colors.gray[500],
    textAlign: 'center',
  },
  webMapFooter: {
    backgroundColor: colors.gray[50],
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  webMapNote: {
    fontSize: 12,
    color: colors.gray[600],
    textAlign: 'center',
    fontStyle: 'italic',
  },
});
