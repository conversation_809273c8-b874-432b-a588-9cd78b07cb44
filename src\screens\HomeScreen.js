import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Image,
  FlatList,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../config/colors';
import { supabase } from '../config/supabase';

export default function HomeScreen({ navigation }) {
  const [user, setUser] = useState(null);
  const [nearbyVenues, setNearbyVenues] = useState([]);
  const [upcomingMatches, setUpcomingMatches] = useState([]);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    getCurrentUser();
    loadHomeData();
  }, []);

  const getCurrentUser = async () => {
    const { data: { user } } = await supabase.auth.getUser();
    setUser(user);
  };

  const loadHomeData = async () => {
    // Simular datos para demo
    setNearbyVenues([
      {
        id: 1,
        name: 'Cancha Los Campeones',
        distance: '0.5 km',
        rating: 4.8,
        price: '$25/hora',
        image: 'https://via.placeholder.com/150x100',
        surface: 'Césped sintético'
      },
      {
        id: 2,
        name: 'Complejo Deportivo Central',
        distance: '1.2 km',
        rating: 4.6,
        price: '$30/hora',
        image: 'https://via.placeholder.com/150x100',
        surface: 'Césped natural'
      },
    ]);

    setUpcomingMatches([
      {
        id: 1,
        title: 'Fútbol 7 - Nivel Intermedio',
        date: 'Hoy 18:00',
        venue: 'Cancha Los Campeones',
        players: '6/14',
        status: 'Faltan 8 jugadores'
      },
      {
        id: 2,
        title: 'Fútbol 11 - Nivel Avanzado',
        date: 'Mañana 20:00',
        venue: 'Complejo Central',
        players: '18/22',
        status: 'Faltan 4 jugadores'
      },
    ]);
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadHomeData();
    setRefreshing(false);
  };

  const renderVenueCard = ({ item }) => (
    <TouchableOpacity
      style={styles.venueCard}
      onPress={() => navigation.navigate('VenueDetail', { venue: item })}
    >
      <Image source={{ uri: item.image }} style={styles.venueImage} />
      <View style={styles.venueInfo}>
        <Text style={styles.venueName}>{item.name}</Text>
        <Text style={styles.venueDetails}>{item.surface}</Text>
        <View style={styles.venueFooter}>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={16} color={colors.warning} />
            <Text style={styles.rating}>{item.rating}</Text>
          </View>
          <Text style={styles.distance}>{item.distance}</Text>
          <Text style={styles.price}>{item.price}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderMatchCard = ({ item }) => (
    <TouchableOpacity style={styles.matchCard}>
      <View style={styles.matchHeader}>
        <Text style={styles.matchTitle}>{item.title}</Text>
        <Text style={styles.matchDate}>{item.date}</Text>
      </View>
      <Text style={styles.matchVenue}>{item.venue}</Text>
      <View style={styles.matchFooter}>
        <Text style={styles.matchPlayers}>{item.players}</Text>
        <Text style={styles.matchStatus}>{item.status}</Text>
      </View>
    </TouchableOpacity>
  );

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>¡Hola!</Text>
          <Text style={styles.userName}>{user?.user_metadata?.full_name || 'Usuario'}</Text>
        </View>
        <TouchableOpacity style={styles.notificationButton}>
          <Ionicons name="notifications-outline" size={24} color={colors.gray[700]} />
        </TouchableOpacity>
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity
          style={styles.actionButton}
          onPress={() => navigation.navigate('Search')}
        >
          <Ionicons name="search" size={24} color={colors.white} />
          <Text style={styles.actionText}>Buscar Cancha</Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.actionButton, styles.actionButtonSecondary]}
          onPress={() => navigation.navigate('Match')}
        >
          <Ionicons name="add" size={24} color={colors.white} />
          <Text style={styles.actionText}>Crear Partido</Text>
        </TouchableOpacity>
      </View>

      {/* Nearby Venues */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Canchas Cercanas</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Search')}>
            <Text style={styles.seeAll}>Ver todas</Text>
          </TouchableOpacity>
        </View>
        <FlatList
          data={nearbyVenues}
          renderItem={renderVenueCard}
          keyExtractor={(item) => item.id.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.venuesList}
        />
      </View>

      {/* Upcoming Matches */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Partidos Próximos</Text>
          <TouchableOpacity onPress={() => navigation.navigate('Match')}>
            <Text style={styles.seeAll}>Ver todos</Text>
          </TouchableOpacity>
        </View>
        <FlatList
          data={upcomingMatches}
          renderItem={renderMatchCard}
          keyExtractor={(item) => item.id.toString()}
          scrollEnabled={false}
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    backgroundColor: colors.white,
  },
  greeting: {
    fontSize: 16,
    color: colors.gray[600],
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.gray[900],
  },
  notificationButton: {
    padding: 8,
  },
  quickActions: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
  },
  actionButton: {
    flex: 1,
    backgroundColor: colors.primary[500],
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  actionButtonSecondary: {
    backgroundColor: colors.secondary[500],
  },
  actionText: {
    color: colors.white,
    fontWeight: '600',
    fontSize: 16,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.gray[900],
  },
  seeAll: {
    color: colors.primary[500],
    fontWeight: '600',
  },
  venuesList: {
    paddingLeft: 20,
  },
  venueCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginRight: 16,
    width: 280,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  venueImage: {
    width: '100%',
    height: 120,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
  },
  venueInfo: {
    padding: 16,
  },
  venueName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 4,
  },
  venueDetails: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 12,
  },
  venueFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
  },
  distance: {
    fontSize: 14,
    color: colors.gray[600],
  },
  price: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary[500],
  },
  matchCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 20,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  matchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  matchTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.gray[900],
    flex: 1,
  },
  matchDate: {
    fontSize: 14,
    color: colors.primary[500],
    fontWeight: '600',
  },
  matchVenue: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 12,
  },
  matchFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  matchPlayers: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[700],
  },
  matchStatus: {
    fontSize: 14,
    color: colors.secondary[600],
    fontWeight: '500',
  },
});
