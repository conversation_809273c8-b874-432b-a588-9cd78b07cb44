import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Image,
  FlatList,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../config/colors';

const { width } = Dimensions.get('window');

export default function VenueDetailScreen({ route, navigation }) {
  const { venue } = route.params;
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [selectedDate, setSelectedDate] = useState('2024-01-15');

  // Datos de ejemplo para demo
  const images = [
    'https://via.placeholder.com/400x250',
    'https://via.placeholder.com/400x250',
    'https://via.placeholder.com/400x250',
  ];

  const reviews = [
    {
      id: 1,
      user: '<PERSON>',
      rating: 5,
      comment: 'Excelente cancha, muy bien mantenida. El césped sintético está en perfectas condiciones.',
      date: '2024-01-10'
    },
    {
      id: 2,
      user: '<PERSON>',
      rating: 4,
      comment: 'Buena ubicación y facilidades. Los vestuarios están limpios.',
      date: '2024-01-08'
    },
  ];

  const availableSlots = [
    { time: '09:00', price: 25, available: true },
    { time: '10:00', price: 25, available: true },
    { time: '11:00', price: 25, available: false },
    { time: '16:00', price: 30, available: true },
    { time: '17:00', price: 30, available: true },
    { time: '18:00', price: 35, available: true },
    { time: '19:00', price: 35, available: false },
    { time: '20:00', price: 35, available: true },
  ];

  const renderImage = ({ item, index }) => (
    <Image source={{ uri: item }} style={styles.carouselImage} />
  );

  const renderReview = ({ item }) => (
    <View style={styles.reviewCard}>
      <View style={styles.reviewHeader}>
        <Text style={styles.reviewUser}>{item.user}</Text>
        <View style={styles.reviewRating}>
          {[...Array(5)].map((_, i) => (
            <Ionicons
              key={i}
              name="star"
              size={14}
              color={i < item.rating ? colors.warning : colors.gray[300]}
            />
          ))}
        </View>
      </View>
      <Text style={styles.reviewComment}>{item.comment}</Text>
      <Text style={styles.reviewDate}>{item.date}</Text>
    </View>
  );

  const renderTimeSlot = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.timeSlot,
        !item.available && styles.timeSlotUnavailable
      ]}
      disabled={!item.available}
      onPress={() => navigation.navigate('Booking', { 
        venue, 
        selectedTime: item.time, 
        price: item.price,
        date: selectedDate 
      })}
    >
      <Text style={[
        styles.timeSlotText,
        !item.available && styles.timeSlotTextUnavailable
      ]}>
        {item.time}
      </Text>
      <Text style={[
        styles.timeSlotPrice,
        !item.available && styles.timeSlotTextUnavailable
      ]}>
        ${item.price}
      </Text>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Image Carousel */}
      <View style={styles.imageContainer}>
        <FlatList
          data={images}
          renderItem={renderImage}
          keyExtractor={(item, index) => index.toString()}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={(event) => {
            const index = Math.round(event.nativeEvent.contentOffset.x / width);
            setSelectedImageIndex(index);
          }}
        />
        <View style={styles.imageIndicators}>
          {images.map((_, index) => (
            <View
              key={index}
              style={[
                styles.indicator,
                index === selectedImageIndex && styles.indicatorActive
              ]}
            />
          ))}
        </View>
      </View>

      {/* Venue Info */}
      <View style={styles.venueInfo}>
        <View style={styles.venueHeader}>
          <View style={styles.venueTitle}>
            <Text style={styles.venueName}>{venue.name}</Text>
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={16} color={colors.warning} />
              <Text style={styles.rating}>{venue.rating}</Text>
              <Text style={styles.reviewCount}>({venue.total_reviews || 12} reseñas)</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.favoriteButton}>
            <Ionicons name="heart-outline" size={24} color={colors.gray[600]} />
          </TouchableOpacity>
        </View>

        <View style={styles.venueDetails}>
          <View style={styles.detailRow}>
            <Ionicons name="location" size={16} color={colors.gray[500]} />
            <Text style={styles.detailText}>{venue.address}</Text>
          </View>
          <View style={styles.detailRow}>
            <Ionicons name="resize" size={16} color={colors.gray[500]} />
            <Text style={styles.detailText}>{venue.surface}</Text>
          </View>
          <View style={styles.detailRow}>
            <Ionicons name="car" size={16} color={colors.gray[500]} />
            <Text style={styles.detailText}>{venue.distance}</Text>
          </View>
        </View>

        {/* Amenities */}
        <View style={styles.amenitiesContainer}>
          <Text style={styles.sectionTitle}>Servicios</Text>
          <View style={styles.amenitiesList}>
            {venue.amenities?.map((amenity, index) => (
              <View key={index} style={styles.amenityTag}>
                <Ionicons name="checkmark-circle" size={16} color={colors.secondary[500]} />
                <Text style={styles.amenityText}>{amenity}</Text>
              </View>
            ))}
          </View>
        </View>

        {/* Available Times */}
        <View style={styles.timeSlotsContainer}>
          <Text style={styles.sectionTitle}>Horarios Disponibles - {selectedDate}</Text>
          <FlatList
            data={availableSlots}
            renderItem={renderTimeSlot}
            keyExtractor={(item) => item.time}
            numColumns={4}
            scrollEnabled={false}
            contentContainerStyle={styles.timeSlotsGrid}
          />
        </View>

        {/* Reviews */}
        <View style={styles.reviewsContainer}>
          <View style={styles.reviewsHeader}>
            <Text style={styles.sectionTitle}>Reseñas</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>Ver todas</Text>
            </TouchableOpacity>
          </View>
          <FlatList
            data={reviews}
            renderItem={renderReview}
            keyExtractor={(item) => item.id.toString()}
            scrollEnabled={false}
          />
        </View>
      </View>

      {/* Bottom Actions */}
      <View style={styles.bottomActions}>
        <TouchableOpacity style={styles.callButton}>
          <Ionicons name="call" size={20} color={colors.primary[500]} />
          <Text style={styles.callButtonText}>Llamar</Text>
        </TouchableOpacity>
        <TouchableOpacity 
          style={styles.bookButton}
          onPress={() => navigation.navigate('Booking', { venue })}
        >
          <Text style={styles.bookButtonText}>Reservar Ahora</Text>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  imageContainer: {
    position: 'relative',
  },
  carouselImage: {
    width: width,
    height: 250,
    resizeMode: 'cover',
  },
  imageIndicators: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 8,
  },
  indicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
  },
  indicatorActive: {
    backgroundColor: colors.white,
  },
  venueInfo: {
    padding: 20,
  },
  venueHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  venueTitle: {
    flex: 1,
  },
  venueName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 8,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  rating: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[700],
  },
  reviewCount: {
    fontSize: 14,
    color: colors.gray[500],
  },
  favoriteButton: {
    padding: 8,
  },
  venueDetails: {
    gap: 8,
    marginBottom: 24,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 16,
    color: colors.gray[600],
  },
  amenitiesContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 12,
  },
  amenitiesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  amenityTag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.secondary[50],
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    gap: 4,
  },
  amenityText: {
    fontSize: 14,
    color: colors.secondary[700],
    fontWeight: '500',
  },
  timeSlotsContainer: {
    marginBottom: 24,
  },
  timeSlotsGrid: {
    gap: 8,
  },
  timeSlot: {
    flex: 1,
    backgroundColor: colors.gray[100],
    borderRadius: 8,
    padding: 12,
    alignItems: 'center',
    margin: 4,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  timeSlotUnavailable: {
    backgroundColor: colors.gray[50],
    opacity: 0.5,
  },
  timeSlotText: {
    fontSize: 14,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 2,
  },
  timeSlotPrice: {
    fontSize: 12,
    color: colors.primary[500],
    fontWeight: '500',
  },
  timeSlotTextUnavailable: {
    color: colors.gray[400],
  },
  reviewsContainer: {
    marginBottom: 100,
  },
  reviewsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  seeAllText: {
    color: colors.primary[500],
    fontWeight: '600',
  },
  reviewCard: {
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reviewUser: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
  },
  reviewRating: {
    flexDirection: 'row',
    gap: 2,
  },
  reviewComment: {
    fontSize: 14,
    color: colors.gray[700],
    lineHeight: 20,
    marginBottom: 8,
  },
  reviewDate: {
    fontSize: 12,
    color: colors.gray[500],
  },
  bottomActions: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    backgroundColor: colors.white,
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    gap: 12,
  },
  callButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.primary[500],
    borderRadius: 8,
    padding: 16,
    gap: 8,
  },
  callButtonText: {
    color: colors.primary[500],
    fontWeight: '600',
    fontSize: 16,
  },
  bookButton: {
    flex: 2,
    backgroundColor: colors.primary[500],
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
  },
  bookButtonText: {
    color: colors.white,
    fontWeight: '600',
    fontSize: 16,
  },
});
