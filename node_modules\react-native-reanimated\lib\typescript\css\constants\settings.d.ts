import type { PredefinedTimingFunction, StepsModifier } from '../easing';
import type { CSSAnimationProp, CSSTransitionProp } from '../types';
export declare const ANIMATION_PROPS: CSSAnimationProp[];
export declare const TRANSITION_PROPS: CSSTransitionProp[];
export declare const VALID_STEPS_MODIFIERS: StepsModifier[];
export declare const VALID_PREDEFINED_TIMING_FUNCTIONS: PredefinedTimingFunction[];
export declare const VALID_PARAMETRIZED_TIMING_FUNCTIONS: string[];
//# sourceMappingURL=settings.d.ts.map