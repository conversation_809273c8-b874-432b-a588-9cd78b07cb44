import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  TextInput,
  Alert,
  Modal,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '../config/colors';

export default function PaymentScreen({ route, navigation }) {
  const { booking } = route.params;
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('card');
  const [cardData, setCardData] = useState({
    number: '',
    expiry: '',
    cvv: '',
    name: '',
  });
  const [showInvoiceModal, setShowInvoiceModal] = useState(false);
  const [invoiceType, setInvoiceType] = useState('boleta');
  const [invoiceData, setInvoiceData] = useState({
    documentType: 'dni',
    documentNumber: '',
    name: '',
    address: '',
    email: '',
  });
  const [processing, setProcessing] = useState(false);

  const paymentMethods = [
    {
      id: 'card',
      name: 'Tarjeta de Crédito/Débito',
      icon: 'card',
      description: 'Visa, Mastercard, American Express'
    },
    {
      id: 'yape',
      name: 'Yape',
      icon: 'phone-portrait',
      description: 'Pago móvil instantáneo'
    },
    {
      id: 'plin',
      name: 'Plin',
      icon: 'phone-portrait',
      description: 'Transferencia móvil'
    },
    {
      id: 'bank',
      name: 'Transferencia Bancaria',
      icon: 'business',
      description: 'BCP, BBVA, Interbank, Scotiabank'
    },
  ];

  const formatCardNumber = (text) => {
    const cleaned = text.replace(/\s/g, '');
    const match = cleaned.match(/.{1,4}/g);
    return match ? match.join(' ') : cleaned;
  };

  const formatExpiry = (text) => {
    const cleaned = text.replace(/\D/g, '');
    if (cleaned.length >= 2) {
      return cleaned.substring(0, 2) + '/' + cleaned.substring(2, 4);
    }
    return cleaned;
  };

  const handleCardInput = (field, value) => {
    let formattedValue = value;
    
    if (field === 'number') {
      formattedValue = formatCardNumber(value);
      if (formattedValue.replace(/\s/g, '').length > 16) return;
    } else if (field === 'expiry') {
      formattedValue = formatExpiry(value);
      if (formattedValue.length > 5) return;
    } else if (field === 'cvv') {
      formattedValue = value.replace(/\D/g, '');
      if (formattedValue.length > 4) return;
    }
    
    setCardData(prev => ({ ...prev, [field]: formattedValue }));
  };

  const validatePayment = () => {
    if (selectedPaymentMethod === 'card') {
      const { number, expiry, cvv, name } = cardData;
      if (!number || !expiry || !cvv || !name) {
        Alert.alert('Error', 'Por favor completa todos los datos de la tarjeta');
        return false;
      }
      if (number.replace(/\s/g, '').length < 16) {
        Alert.alert('Error', 'Número de tarjeta inválido');
        return false;
      }
    }
    return true;
  };

  const processPayment = async () => {
    if (!validatePayment()) return;

    setProcessing(true);
    
    // Simular procesamiento de pago
    setTimeout(() => {
      setProcessing(false);
      setShowInvoiceModal(true);
    }, 2000);
  };

  const generateInvoice = () => {
    // Aquí iría la lógica para generar la factura/boleta
    Alert.alert(
      '¡Pago Exitoso!',
      `Tu ${invoiceType} ha sido generada y enviada a tu email. La reserva está confirmada.`,
      [
        {
          text: 'Ver Comprobante',
          onPress: () => {
            setShowInvoiceModal(false);
            // Aquí se abriría el PDF del comprobante
            Alert.alert('Comprobante', 'El comprobante se ha descargado exitosamente');
          }
        },
        {
          text: 'Continuar',
          onPress: () => {
            setShowInvoiceModal(false);
            navigation.navigate('Main');
          }
        }
      ]
    );
  };

  const InvoiceModal = () => (
    <Modal visible={showInvoiceModal} animationType="slide" transparent>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Datos para Comprobante</Text>
          </View>

          <ScrollView style={styles.invoiceForm}>
            <Text style={styles.label}>Tipo de Comprobante</Text>
            <View style={styles.invoiceTypeContainer}>
              <TouchableOpacity
                style={[
                  styles.invoiceTypeButton,
                  invoiceType === 'boleta' && styles.invoiceTypeButtonActive
                ]}
                onPress={() => setInvoiceType('boleta')}
              >
                <Text style={[
                  styles.invoiceTypeText,
                  invoiceType === 'boleta' && styles.invoiceTypeTextActive
                ]}>
                  Boleta
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.invoiceTypeButton,
                  invoiceType === 'factura' && styles.invoiceTypeButtonActive
                ]}
                onPress={() => setInvoiceType('factura')}
              >
                <Text style={[
                  styles.invoiceTypeText,
                  invoiceType === 'factura' && styles.invoiceTypeTextActive
                ]}>
                  Factura
                </Text>
              </TouchableOpacity>
            </View>

            <Text style={styles.label}>
              {invoiceType === 'factura' ? 'RUC' : 'Documento de Identidad'}
            </Text>
            <TextInput
              style={styles.input}
              value={invoiceData.documentNumber}
              onChangeText={(text) => setInvoiceData(prev => ({ ...prev, documentNumber: text }))}
              placeholder={invoiceType === 'factura' ? 'Ingresa tu RUC' : 'Ingresa tu DNI'}
              keyboardType="numeric"
            />

            <Text style={styles.label}>
              {invoiceType === 'factura' ? 'Razón Social' : 'Nombre Completo'}
            </Text>
            <TextInput
              style={styles.input}
              value={invoiceData.name}
              onChangeText={(text) => setInvoiceData(prev => ({ ...prev, name: text }))}
              placeholder={invoiceType === 'factura' ? 'Razón social de la empresa' : 'Tu nombre completo'}
            />

            {invoiceType === 'factura' && (
              <>
                <Text style={styles.label}>Dirección Fiscal</Text>
                <TextInput
                  style={styles.input}
                  value={invoiceData.address}
                  onChangeText={(text) => setInvoiceData(prev => ({ ...prev, address: text }))}
                  placeholder="Dirección fiscal de la empresa"
                />
              </>
            )}

            <Text style={styles.label}>Email</Text>
            <TextInput
              style={styles.input}
              value={invoiceData.email}
              onChangeText={(text) => setInvoiceData(prev => ({ ...prev, email: text }))}
              placeholder="Email para enviar el comprobante"
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </ScrollView>

          <TouchableOpacity style={styles.generateButton} onPress={generateInvoice}>
            <Text style={styles.generateButtonText}>Generar Comprobante</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  return (
    <ScrollView style={styles.container}>
      {/* Booking Summary */}
      <View style={styles.summaryCard}>
        <Text style={styles.sectionTitle}>Resumen de Pago</Text>
        <View style={styles.summaryRow}>
          <Text style={styles.summaryLabel}>{booking.venue.name}</Text>
          <Text style={styles.summaryValue}>${booking.totalAmount}</Text>
        </View>
        <Text style={styles.summaryDetails}>
          {booking.date} • {booking.time} • {booking.duration / 60}h
        </Text>
        {booking.splitPayment && (
          <View style={styles.splitInfo}>
            <Ionicons name="people" size={16} color={colors.secondary[500]} />
            <Text style={styles.splitText}>
              Pago dividido entre {booking.maxPlayers} jugadores (${booking.amountPerPlayer} c/u)
            </Text>
          </View>
        )}
      </View>

      {/* Payment Methods */}
      <View style={styles.paymentMethodsCard}>
        <Text style={styles.sectionTitle}>Método de Pago</Text>
        {paymentMethods.map((method) => (
          <TouchableOpacity
            key={method.id}
            style={[
              styles.paymentMethod,
              selectedPaymentMethod === method.id && styles.paymentMethodActive
            ]}
            onPress={() => setSelectedPaymentMethod(method.id)}
          >
            <View style={styles.paymentMethodLeft}>
              <View style={[
                styles.paymentMethodIcon,
                selectedPaymentMethod === method.id && styles.paymentMethodIconActive
              ]}>
                <Ionicons 
                  name={method.icon} 
                  size={24} 
                  color={selectedPaymentMethod === method.id ? colors.white : colors.gray[600]} 
                />
              </View>
              <View style={styles.paymentMethodInfo}>
                <Text style={styles.paymentMethodName}>{method.name}</Text>
                <Text style={styles.paymentMethodDescription}>{method.description}</Text>
              </View>
            </View>
            <View style={[
              styles.radioButton,
              selectedPaymentMethod === method.id && styles.radioButtonActive
            ]}>
              {selectedPaymentMethod === method.id && (
                <View style={styles.radioButtonInner} />
              )}
            </View>
          </TouchableOpacity>
        ))}
      </View>

      {/* Card Details */}
      {selectedPaymentMethod === 'card' && (
        <View style={styles.cardDetailsCard}>
          <Text style={styles.sectionTitle}>Datos de la Tarjeta</Text>
          
          <Text style={styles.label}>Número de Tarjeta</Text>
          <TextInput
            style={styles.input}
            value={cardData.number}
            onChangeText={(text) => handleCardInput('number', text)}
            placeholder="1234 5678 9012 3456"
            keyboardType="numeric"
          />

          <View style={styles.cardRow}>
            <View style={styles.cardField}>
              <Text style={styles.label}>Vencimiento</Text>
              <TextInput
                style={styles.input}
                value={cardData.expiry}
                onChangeText={(text) => handleCardInput('expiry', text)}
                placeholder="MM/AA"
                keyboardType="numeric"
              />
            </View>
            <View style={styles.cardField}>
              <Text style={styles.label}>CVV</Text>
              <TextInput
                style={styles.input}
                value={cardData.cvv}
                onChangeText={(text) => handleCardInput('cvv', text)}
                placeholder="123"
                keyboardType="numeric"
                secureTextEntry
              />
            </View>
          </View>

          <Text style={styles.label}>Nombre del Titular</Text>
          <TextInput
            style={styles.input}
            value={cardData.name}
            onChangeText={(text) => handleCardInput('name', text)}
            placeholder="Nombre como aparece en la tarjeta"
            autoCapitalize="words"
          />
        </View>
      )}

      {/* Security Info */}
      <View style={styles.securityCard}>
        <View style={styles.securityHeader}>
          <Ionicons name="shield-checkmark" size={24} color={colors.secondary[500]} />
          <Text style={styles.securityTitle}>Pago Seguro</Text>
        </View>
        <Text style={styles.securityText}>
          Tu información está protegida con encriptación de 256 bits. 
          No almacenamos datos de tarjetas de crédito.
        </Text>
      </View>

      {/* Pay Button */}
      <TouchableOpacity 
        style={[styles.payButton, processing && styles.payButtonDisabled]} 
        onPress={processPayment}
        disabled={processing}
      >
        {processing ? (
          <Text style={styles.payButtonText}>Procesando...</Text>
        ) : (
          <>
            <Text style={styles.payButtonText}>
              {booking.splitPayment ? 'Confirmar Reserva' : 'Pagar Ahora'}
            </Text>
            <Text style={styles.payButtonAmount}>
              ${booking.splitPayment ? booking.amountPerPlayer : booking.totalAmount}
            </Text>
          </>
        )}
      </TouchableOpacity>

      <InvoiceModal />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  summaryCard: {
    backgroundColor: colors.white,
    margin: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 16,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  summaryLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.primary[500],
  },
  summaryDetails: {
    fontSize: 14,
    color: colors.gray[600],
    marginBottom: 8,
  },
  splitInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: colors.secondary[50],
    padding: 12,
    borderRadius: 8,
  },
  splitText: {
    fontSize: 14,
    color: colors.secondary[700],
    flex: 1,
  },
  paymentMethodsCard: {
    backgroundColor: colors.white,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  paymentMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[200],
    marginBottom: 12,
  },
  paymentMethodActive: {
    borderColor: colors.primary[500],
    backgroundColor: colors.primary[50],
  },
  paymentMethodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  paymentMethodIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.gray[100],
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  paymentMethodIconActive: {
    backgroundColor: colors.primary[500],
  },
  paymentMethodInfo: {
    flex: 1,
  },
  paymentMethodName: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 2,
  },
  paymentMethodDescription: {
    fontSize: 14,
    color: colors.gray[600],
  },
  radioButton: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: colors.gray[300],
    justifyContent: 'center',
    alignItems: 'center',
  },
  radioButtonActive: {
    borderColor: colors.primary[500],
  },
  radioButtonInner: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.primary[500],
  },
  cardDetailsCard: {
    backgroundColor: colors.white,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 8,
    marginTop: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: colors.white,
  },
  cardRow: {
    flexDirection: 'row',
    gap: 12,
  },
  cardField: {
    flex: 1,
  },
  securityCard: {
    backgroundColor: colors.white,
    marginHorizontal: 16,
    marginBottom: 100,
    borderRadius: 12,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  securityHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  securityTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
  },
  securityText: {
    fontSize: 14,
    color: colors.gray[600],
    lineHeight: 20,
  },
  payButton: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: colors.primary[500],
    margin: 16,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  payButtonDisabled: {
    backgroundColor: colors.gray[400],
  },
  payButtonText: {
    color: colors.white,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  payButtonAmount: {
    color: colors.white,
    fontSize: 14,
    opacity: 0.9,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.gray[900],
  },
  invoiceForm: {
    padding: 20,
  },
  invoiceTypeContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  invoiceTypeButton: {
    flex: 1,
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[300],
    alignItems: 'center',
  },
  invoiceTypeButtonActive: {
    backgroundColor: colors.primary[500],
    borderColor: colors.primary[500],
  },
  invoiceTypeText: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[700],
  },
  invoiceTypeTextActive: {
    color: colors.white,
  },
  generateButton: {
    backgroundColor: colors.primary[500],
    margin: 20,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  generateButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
