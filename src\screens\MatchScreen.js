import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Calendar } from 'react-native-calendars';
import { colors } from '../config/colors';
import { supabase } from '../config/supabase';

export default function MatchScreen({ navigation }) {
  const [activeTab, setActiveTab] = useState('available'); // available, my_matches, create
  const [matches, setMatches] = useState([]);
  const [myMatches, setMyMatches] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showCalendar, setShowCalendar] = useState(false);
  const [newMatch, setNewMatch] = useState({
    sport_type: 'futbol',
    skill_level: 'intermediate',
    max_players: 14,
    duration_minutes: 90,
    description: '',
    venue_id: null,
    match_date: '',
    total_cost: 0,
  });

  useEffect(() => {
    loadMatches();
    loadMyMatches();
  }, []);

  const loadMatches = () => {
    // Datos de ejemplo para demo
    setMatches([
      {
        id: 1,
        title: 'Fútbol 7 - Nivel Intermedio',
        sport_type: 'futbol',
        skill_level: 'intermediate',
        match_date: '2024-01-15T18:00:00',
        venue: 'Cancha Los Campeones',
        venue_address: 'Av. Principal 123',
        max_players: 14,
        current_players: 6,
        cost_per_player: 12.5,
        total_cost: 175,
        creator: 'Juan Pérez',
        description: 'Partido amistoso, buen ambiente. Todos los niveles bienvenidos.',
        status: 'open'
      },
      {
        id: 2,
        title: 'Fútbol 11 - Nivel Avanzado',
        sport_type: 'futbol',
        skill_level: 'advanced',
        match_date: '2024-01-16T20:00:00',
        venue: 'Complejo Deportivo Central',
        venue_address: 'Jr. Deportes 456',
        max_players: 22,
        current_players: 18,
        cost_per_player: 15,
        total_cost: 330,
        creator: 'María García',
        description: 'Partido competitivo, se requiere experiencia.',
        status: 'open'
      },
      {
        id: 3,
        title: 'Futsal - Principiantes',
        sport_type: 'futsal',
        skill_level: 'beginner',
        match_date: '2024-01-17T19:00:00',
        venue: 'Arena Norte',
        venue_address: 'Av. Norte 789',
        max_players: 10,
        current_players: 8,
        cost_per_player: 10,
        total_cost: 100,
        creator: 'Carlos López',
        description: 'Perfecto para quienes están empezando.',
        status: 'open'
      },
    ]);
  };

  const loadMyMatches = () => {
    // Datos de ejemplo para demo
    setMyMatches([
      {
        id: 4,
        title: 'Mi Partido - Fútbol 7',
        sport_type: 'futbol',
        skill_level: 'intermediate',
        match_date: '2024-01-18T17:00:00',
        venue: 'Cancha Los Campeones',
        max_players: 14,
        current_players: 10,
        cost_per_player: 12.5,
        total_cost: 175,
        creator: 'Yo',
        status: 'open',
        role: 'creator'
      },
      {
        id: 5,
        title: 'Partido Confirmado',
        sport_type: 'futbol',
        skill_level: 'advanced',
        match_date: '2024-01-19T20:00:00',
        venue: 'Complejo Central',
        max_players: 22,
        current_players: 22,
        cost_per_player: 15,
        total_cost: 330,
        creator: 'Ana Martín',
        status: 'confirmed',
        role: 'participant'
      },
    ]);
  };

  const joinMatch = (matchId) => {
    Alert.alert(
      'Unirse al Partido',
      '¿Estás seguro que quieres unirte a este partido?',
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Unirse',
          onPress: () => {
            // Aquí iría la lógica para unirse al partido
            Alert.alert('¡Éxito!', 'Te has unido al partido correctamente');
          }
        }
      ]
    );
  };

  const createMatch = () => {
    if (!newMatch.match_date || !newMatch.venue_id) {
      Alert.alert('Error', 'Por favor completa todos los campos requeridos');
      return;
    }

    // Aquí iría la lógica para crear el partido
    Alert.alert('¡Éxito!', 'Partido creado correctamente', [
      {
        text: 'OK',
        onPress: () => {
          setShowCreateModal(false);
          setNewMatch({
            sport_type: 'futbol',
            skill_level: 'intermediate',
            max_players: 14,
            duration_minutes: 90,
            description: '',
            venue_id: null,
            match_date: '',
            total_cost: 0,
          });
        }
      }
    ]);
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('es-ES', {
      weekday: 'short',
      day: 'numeric',
      month: 'short',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getSkillLevelColor = (level) => {
    switch (level) {
      case 'beginner': return colors.secondary[500];
      case 'intermediate': return colors.warning;
      case 'advanced': return colors.primary[500];
      default: return colors.gray[500];
    }
  };

  const getSkillLevelText = (level) => {
    switch (level) {
      case 'beginner': return 'Principiante';
      case 'intermediate': return 'Intermedio';
      case 'advanced': return 'Avanzado';
      default: return level;
    }
  };

  const renderMatchCard = ({ item }) => (
    <TouchableOpacity style={styles.matchCard}>
      <View style={styles.matchHeader}>
        <View style={styles.matchInfo}>
          <Text style={styles.matchTitle}>{item.title}</Text>
          <View style={styles.skillBadge}>
            <View style={[styles.skillDot, { backgroundColor: getSkillLevelColor(item.skill_level) }]} />
            <Text style={styles.skillText}>{getSkillLevelText(item.skill_level)}</Text>
          </View>
        </View>
        <Text style={styles.matchDate}>{formatDate(item.match_date)}</Text>
      </View>

      <View style={styles.venueInfo}>
        <Ionicons name="location" size={16} color={colors.gray[500]} />
        <Text style={styles.venueText}>{item.venue}</Text>
      </View>

      <Text style={styles.matchDescription} numberOfLines={2}>
        {item.description}
      </Text>

      <View style={styles.matchStats}>
        <View style={styles.playersInfo}>
          <Ionicons name="people" size={16} color={colors.gray[600]} />
          <Text style={styles.playersText}>
            {item.current_players}/{item.max_players} jugadores
          </Text>
        </View>
        <Text style={styles.costText}>${item.cost_per_player}/persona</Text>
      </View>

      <View style={styles.matchFooter}>
        <Text style={styles.creatorText}>Creado por: {item.creator}</Text>
        {activeTab === 'available' && (
          <TouchableOpacity
            style={styles.joinButton}
            onPress={() => joinMatch(item.id)}
          >
            <Text style={styles.joinButtonText}>Unirse</Text>
          </TouchableOpacity>
        )}
        {activeTab === 'my_matches' && (
          <TouchableOpacity
            style={styles.chatButton}
            onPress={() => navigation.navigate('Chat', { matchId: item.id })}
          >
            <Ionicons name="chatbubble" size={16} color={colors.white} />
            <Text style={styles.chatButtonText}>Chat</Text>
          </TouchableOpacity>
        )}
      </View>

      {item.current_players < item.max_players && (
        <View style={styles.needPlayersBar}>
          <Text style={styles.needPlayersText}>
            Faltan {item.max_players - item.current_players} jugadores
          </Text>
        </View>
      )}
    </TouchableOpacity>
  );

  const CreateMatchModal = () => (
    <Modal visible={showCreateModal} animationType="slide" transparent>
      <View style={styles.modalOverlay}>
        <View style={styles.modalContent}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Crear Partido</Text>
            <TouchableOpacity onPress={() => setShowCreateModal(false)}>
              <Ionicons name="close" size={24} color={colors.gray[600]} />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.formContent}>
            <Text style={styles.label}>Deporte</Text>
            <View style={styles.optionsRow}>
              {['futbol', 'futsal', 'basquet'].map(sport => (
                <TouchableOpacity
                  key={sport}
                  style={[
                    styles.option,
                    newMatch.sport_type === sport && styles.optionActive
                  ]}
                  onPress={() => setNewMatch(prev => ({ ...prev, sport_type: sport }))}
                >
                  <Text style={[
                    styles.optionText,
                    newMatch.sport_type === sport && styles.optionTextActive
                  ]}>
                    {sport.charAt(0).toUpperCase() + sport.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.label}>Nivel</Text>
            <View style={styles.optionsRow}>
              {[
                { key: 'beginner', label: 'Principiante' },
                { key: 'intermediate', label: 'Intermedio' },
                { key: 'advanced', label: 'Avanzado' }
              ].map(level => (
                <TouchableOpacity
                  key={level.key}
                  style={[
                    styles.option,
                    newMatch.skill_level === level.key && styles.optionActive
                  ]}
                  onPress={() => setNewMatch(prev => ({ ...prev, skill_level: level.key }))}
                >
                  <Text style={[
                    styles.optionText,
                    newMatch.skill_level === level.key && styles.optionTextActive
                  ]}>
                    {level.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            <Text style={styles.label}>Fecha y Hora</Text>
            <TouchableOpacity
              style={styles.dateButton}
              onPress={() => setShowCalendar(true)}
            >
              <Text style={styles.dateButtonText}>
                {newMatch.match_date || 'Seleccionar fecha'}
              </Text>
              <Ionicons name="calendar" size={20} color={colors.gray[600]} />
            </TouchableOpacity>

            <Text style={styles.label}>Descripción</Text>
            <TextInput
              style={styles.textArea}
              value={newMatch.description}
              onChangeText={(text) => setNewMatch(prev => ({ ...prev, description: text }))}
              placeholder="Describe tu partido..."
              multiline
              numberOfLines={3}
            />
          </ScrollView>

          <TouchableOpacity style={styles.createButton} onPress={createMatch}>
            <Text style={styles.createButtonText}>Crear Partido</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );

  return (
    <View style={styles.container}>
      {/* Tabs */}
      <View style={styles.tabs}>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'available' && styles.tabActive]}
          onPress={() => setActiveTab('available')}
        >
          <Text style={[styles.tabText, activeTab === 'available' && styles.tabTextActive]}>
            Disponibles
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === 'my_matches' && styles.tabActive]}
          onPress={() => setActiveTab('my_matches')}
        >
          <Text style={[styles.tabText, activeTab === 'my_matches' && styles.tabTextActive]}>
            Mis Partidos
          </Text>
        </TouchableOpacity>
      </View>

      {/* Content */}
      <FlatList
        data={activeTab === 'available' ? matches : myMatches}
        renderItem={renderMatchCard}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.matchesList}
        showsVerticalScrollIndicator={false}
      />

      {/* Create Button */}
      <TouchableOpacity
        style={styles.fab}
        onPress={() => setShowCreateModal(true)}
      >
        <Ionicons name="add" size={24} color={colors.white} />
      </TouchableOpacity>

      <CreateMatchModal />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  },
  tabs: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  tab: {
    flex: 1,
    paddingVertical: 16,
    alignItems: 'center',
  },
  tabActive: {
    borderBottomWidth: 2,
    borderBottomColor: colors.primary[500],
  },
  tabText: {
    fontSize: 16,
    fontWeight: '500',
    color: colors.gray[600],
  },
  tabTextActive: {
    color: colors.primary[500],
    fontWeight: '600',
  },
  matchesList: {
    padding: 16,
  },
  matchCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  matchHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  matchInfo: {
    flex: 1,
  },
  matchTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: colors.gray[900],
    marginBottom: 4,
  },
  skillBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  skillDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  skillText: {
    fontSize: 12,
    color: colors.gray[600],
    fontWeight: '500',
  },
  matchDate: {
    fontSize: 14,
    color: colors.primary[500],
    fontWeight: '600',
  },
  venueInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 8,
  },
  venueText: {
    fontSize: 14,
    color: colors.gray[600],
  },
  matchDescription: {
    fontSize: 14,
    color: colors.gray[700],
    marginBottom: 12,
    lineHeight: 20,
  },
  matchStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  playersInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  playersText: {
    fontSize: 14,
    color: colors.gray[600],
    fontWeight: '500',
  },
  costText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: colors.primary[500],
  },
  matchFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  creatorText: {
    fontSize: 12,
    color: colors.gray[500],
    flex: 1,
  },
  joinButton: {
    backgroundColor: colors.secondary[500],
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 6,
  },
  joinButtonText: {
    color: colors.white,
    fontWeight: '600',
    fontSize: 14,
  },
  chatButton: {
    backgroundColor: colors.primary[500],
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 6,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  chatButtonText: {
    color: colors.white,
    fontWeight: '600',
    fontSize: 14,
  },
  needPlayersBar: {
    backgroundColor: colors.warning,
    marginTop: 12,
    marginHorizontal: -16,
    marginBottom: -16,
    padding: 8,
    borderBottomLeftRadius: 12,
    borderBottomRightRadius: 12,
  },
  needPlayersText: {
    color: colors.white,
    fontSize: 12,
    fontWeight: '600',
    textAlign: 'center',
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    backgroundColor: colors.primary[500],
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: colors.gray[900],
  },
  formContent: {
    padding: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: colors.gray[900],
    marginBottom: 8,
    marginTop: 16,
  },
  optionsRow: {
    flexDirection: 'row',
    gap: 8,
  },
  option: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[300],
    alignItems: 'center',
  },
  optionActive: {
    backgroundColor: colors.primary[500],
    borderColor: colors.primary[500],
  },
  optionText: {
    color: colors.gray[700],
    fontWeight: '500',
  },
  optionTextActive: {
    color: colors.white,
  },
  dateButton: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
  },
  dateButtonText: {
    fontSize: 16,
    color: colors.gray[700],
  },
  textArea: {
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    textAlignVertical: 'top',
    minHeight: 80,
  },
  createButton: {
    backgroundColor: colors.primary[500],
    margin: 20,
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  createButtonText: {
    color: colors.white,
    fontSize: 16,
    fontWeight: '600',
  },
});
