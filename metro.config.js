const { getDefaultConfig } = require('expo/metro-config');
const { withNativeWind } = require('nativewind/metro');

const config = getDefaultConfig(__dirname);

// Configurar alias para react-native-maps en web
config.resolver.alias = {
  ...(config.resolver.alias || {}),
};

// Solo aplicar el alias en web
if (process.env.EXPO_PLATFORM === 'web') {
  config.resolver.alias['react-native-maps'] = require.resolve('./src/mocks/react-native-maps-web.js');
}

module.exports = withNativeWind(config, { input: './global.css' });
