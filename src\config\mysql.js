import mysql from 'mysql2/promise';

import { Platform } from 'react-native';

// Configuración de MySQL
const dbConfig = {
  host: Platform.OS === 'web' ? 'localhost' : '*************', // localhost para web, IP para móvil
  user: 'root',
  password: '123456789',
  database: 'futbol_app',
  charset: 'utf8mb4',
  timezone: '+00:00',
  acquireTimeout: 60000,
  timeout: 60000,
  reconnect: true
};

// Pool de conexiones para mejor rendimiento
const pool = mysql.createPool({
  ...dbConfig,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Función para ejecutar consultas
export const query = async (sql, params = []) => {
  try {
    const [rows] = await pool.execute(sql, params);
    return rows;
  } catch (error) {
    console.error('Error en consulta MySQL:', error);
    throw error;
  }
};

// Función para obtener una conexión del pool
export const getConnection = async () => {
  try {
    return await pool.getConnection();
  } catch (error) {
    console.error('Error al obtener conexión MySQL:', error);
    throw error;
  }
};

// Función para cerrar el pool (usar al cerrar la app)
export const closePool = async () => {
  try {
    await pool.end();
  } catch (error) {
    console.error('Error al cerrar pool MySQL:', error);
  }
};

// Funciones de utilidad para la base de datos
export const db = {
  // Usuarios
  users: {
    create: async (userData) => {
      const sql = `
        INSERT INTO users (email, password_hash, full_name, phone) 
        VALUES (?, ?, ?, ?)
      `;
      return await query(sql, [userData.email, userData.password_hash, userData.full_name, userData.phone]);
    },
    
    findByEmail: async (email) => {
      const sql = 'SELECT * FROM users WHERE email = ?';
      const result = await query(sql, [email]);
      return result[0];
    },
    
    findById: async (id) => {
      const sql = 'SELECT * FROM users WHERE id = ?';
      const result = await query(sql, [id]);
      return result[0];
    },
    
    update: async (id, userData) => {
      const fields = Object.keys(userData).map(key => `${key} = ?`).join(', ');
      const values = Object.values(userData);
      const sql = `UPDATE users SET ${fields} WHERE id = ?`;
      return await query(sql, [...values, id]);
    }
  },

  // Canchas
  venues: {
    getAll: async () => {
      const sql = 'SELECT * FROM venues WHERE is_active = TRUE ORDER BY name';
      return await query(sql);
    },
    
    getById: async (id) => {
      const sql = 'SELECT * FROM venues WHERE id = ? AND is_active = TRUE';
      const result = await query(sql, [id]);
      return result[0];
    },
    
    getNearby: async (lat, lng, radius = 10) => {
      const sql = `
        SELECT *, 
        (6371 * acos(cos(radians(?)) * cos(radians(latitude)) * 
        cos(radians(longitude) - radians(?)) + sin(radians(?)) * 
        sin(radians(latitude)))) AS distance 
        FROM venues 
        WHERE is_active = TRUE 
        HAVING distance < ? 
        ORDER BY distance
      `;
      return await query(sql, [lat, lng, lat, radius]);
    },
    
    search: async (searchTerm, filters = {}) => {
      let sql = `
        SELECT * FROM venues 
        WHERE is_active = TRUE 
        AND (name LIKE ? OR address LIKE ? OR description LIKE ?)
      `;
      let params = [`%${searchTerm}%`, `%${searchTerm}%`, `%${searchTerm}%`];
      
      if (filters.surface_type) {
        sql += ' AND surface_type = ?';
        params.push(filters.surface_type);
      }
      
      if (filters.min_price) {
        sql += ' AND base_price_hour >= ?';
        params.push(filters.min_price);
      }
      
      if (filters.max_price) {
        sql += ' AND base_price_hour <= ?';
        params.push(filters.max_price);
      }
      
      sql += ' ORDER BY rating DESC, name';
      return await query(sql, params);
    }
  },

  // Partidos
  matches: {
    getAvailable: async () => {
      const sql = `
        SELECT m.*, v.name as venue_name, v.address as venue_address,
        u.full_name as creator_name
        FROM matches m
        JOIN venues v ON m.venue_id = v.id
        JOIN users u ON m.creator_id = u.id
        WHERE m.status = 'open' AND m.match_date > NOW()
        ORDER BY m.match_date
      `;
      return await query(sql);
    },
    
    getByUser: async (userId) => {
      const sql = `
        SELECT m.*, v.name as venue_name, v.address as venue_address,
        u.full_name as creator_name,
        CASE WHEN m.creator_id = ? THEN 'creator' ELSE 'participant' END as role
        FROM matches m
        JOIN venues v ON m.venue_id = v.id
        JOIN users u ON m.creator_id = u.id
        LEFT JOIN match_participants mp ON m.id = mp.match_id
        WHERE m.creator_id = ? OR mp.user_id = ?
        ORDER BY m.match_date
      `;
      return await query(sql, [userId, userId, userId]);
    },
    
    create: async (matchData) => {
      const sql = `
        INSERT INTO matches (creator_id, venue_id, sport_type, skill_level, 
        match_date, duration_minutes, max_players, total_cost, description) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
      `;
      return await query(sql, [
        matchData.creator_id, matchData.venue_id, matchData.sport_type,
        matchData.skill_level, matchData.match_date, matchData.duration_minutes,
        matchData.max_players, matchData.total_cost, matchData.description
      ]);
    },
    
    joinMatch: async (matchId, userId, amountToPay) => {
      const connection = await getConnection();
      try {
        await connection.beginTransaction();
        
        // Agregar participante
        await connection.execute(
          'INSERT INTO match_participants (match_id, user_id, amount_to_pay) VALUES (?, ?, ?)',
          [matchId, userId, amountToPay]
        );
        
        // Actualizar contador de jugadores
        await connection.execute(
          'UPDATE matches SET current_players = current_players + 1 WHERE id = ?',
          [matchId]
        );
        
        await connection.commit();
        return true;
      } catch (error) {
        await connection.rollback();
        throw error;
      } finally {
        connection.release();
      }
    }
  },

  // Reservas
  reservations: {
    create: async (reservationData) => {
      const sql = `
        INSERT INTO reservations (user_id, venue_id, match_id, start_time, 
        end_time, total_amount, is_split_payment) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;
      return await query(sql, [
        reservationData.user_id, reservationData.venue_id, reservationData.match_id,
        reservationData.start_time, reservationData.end_time, reservationData.total_amount,
        reservationData.is_split_payment
      ]);
    },
    
    getByUser: async (userId) => {
      const sql = `
        SELECT r.*, v.name as venue_name, v.address as venue_address
        FROM reservations r
        JOIN venues v ON r.venue_id = v.id
        WHERE r.user_id = ?
        ORDER BY r.start_time DESC
      `;
      return await query(sql, [userId]);
    }
  },

  // Chat
  chat: {
    getMessages: async (roomId) => {
      const sql = `
        SELECT cm.*, u.full_name as user_name
        FROM chat_messages cm
        LEFT JOIN users u ON cm.user_id = u.id
        WHERE cm.room_id = ?
        ORDER BY cm.created_at
      `;
      return await query(sql, [roomId]);
    },
    
    sendMessage: async (roomId, userId, message, messageType = 'text') => {
      const sql = `
        INSERT INTO chat_messages (room_id, user_id, message, message_type) 
        VALUES (?, ?, ?, ?)
      `;
      return await query(sql, [roomId, userId, message, messageType]);
    },
    
    createRoom: async (matchId) => {
      const sql = 'INSERT INTO chat_rooms (match_id) VALUES (?)';
      return await query(sql, [matchId]);
    }
  }
};

export default db;
